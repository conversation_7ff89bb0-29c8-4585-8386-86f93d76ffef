import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/organization.dart';

part 'additional_data_state.freezed.dart';

@freezed
class AdditionalDataState with _$AdditionalDataState {
  const factory AdditionalDataState.initial() = AdditionalDataInitial;

  const factory AdditionalDataState.loading() = AdditionalDataLoading;

  const factory AdditionalDataState.loaded({
    required List<Organization> governmentAgencies,
    required List<Organization> ministries,
    required List<Organization> departments,
  }) = AdditionalDataLoaded;

  const factory AdditionalDataState.error({required String message}) =
      AdditionalDataError;

  const factory AdditionalDataState.validationError({
    String? firstNameError,
    String? lastNameError,
    String? sectorTypeError,
    String? governmentAgencyError,
    String? departmentNameError,
  }) = AdditionalDataValidationError;

  const factory AdditionalDataState.valid() = AdditionalDataValid;
}
