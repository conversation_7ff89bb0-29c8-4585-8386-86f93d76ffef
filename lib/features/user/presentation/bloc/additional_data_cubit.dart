import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_member_types.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_government_sectors.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../../domain/entities/additional_data.dart';
import '../../domain/entities/organization.dart';
import 'additional_data_state.dart';

class AdditionalDataCubit extends Cubit<AdditionalDataState> {
  final GetMemberTypes _getMemberTypes;
  final GetGovernmentSectors _getGovernmentSectors;

  AdditionalDataCubit(this._getMemberTypes, this._getGovernmentSectors)
    : super(const AdditionalDataState.initial());

  // Current form data
  String _firstName = '';
  String _lastName = '';
  MemberType? _selectedMemberType;
  GovernmentSector? _selectedGovernmentAgency;
  Organization? _selectedMinistry;
  Organization? _selectedDepartment;
  String _departmentName = '';

  // Available options
  List<MemberType> _memberTypes = [];
  List<GovernmentSector> _governmentAgencies = [];
  List<Organization> _ministries = [];
  List<Organization> _departments = [];

  // Getters for current form data
  String get firstName => _firstName;
  String get lastName => _lastName;
  MemberType? get selectedMemberType => _selectedMemberType;
  GovernmentSector? get selectedGovernmentAgency => _selectedGovernmentAgency;
  Organization? get selectedMinistry => _selectedMinistry;
  Organization? get selectedDepartment => _selectedDepartment;
  String get departmentName => _departmentName;

  // Getters for available options
  List<MemberType> get memberTypes => _memberTypes;
  List<GovernmentSector> get governmentAgencies => _governmentAgencies;
  List<Organization> get ministries => _ministries;
  List<Organization> get departments => _departments;

  /// Initialize with data from API
  void initialize() async {
    emit(const AdditionalDataState.loading());

    try {
      // Get member types from API
      final memberTypesResult = await _getMemberTypes(NoParams());

      // Get government sectors from API
      final governmentSectorsResult = await _getGovernmentSectors(NoParams());

      // Check if both API calls succeeded
      if (memberTypesResult.isLeft() || governmentSectorsResult.isLeft()) {
        final memberTypesFailure = memberTypesResult.fold(
          (l) => l,
          (r) => null,
        );
        final governmentSectorsFailure = governmentSectorsResult.fold(
          (l) => l,
          (r) => null,
        );

        final errorMessage =
            memberTypesFailure?.message ??
            governmentSectorsFailure?.message ??
            'Failed to load data';
        emit(AdditionalDataState.error(message: errorMessage));
        return;
      }

      // Extract successful results
      final memberTypes = memberTypesResult.fold(
        (l) => <MemberType>[],
        (r) => r,
      );
      final governmentSectors = governmentSectorsResult.fold(
        (l) => <GovernmentSector>[],
        (r) => r,
      );

      _memberTypes = memberTypes;
      _governmentAgencies = governmentSectors;

      // Mock data for ministries and departments - in real implementation, this would come from API
      _ministries = [
        const Organization(
          id: 1,
          name: 'ministry_finance',
          displayName: 'กระทรวงการคลัง',
        ),
        const Organization(
          id: 2,
          name: 'ministry_interior',
          displayName: 'กระทรวงมหาดไทย',
        ),
        const Organization(
          id: 3,
          name: 'ministry_education',
          displayName: 'กระทรวงศึกษาธิการ',
        ),
      ];

      _departments = [
        const Organization(
          id: 1,
          name: 'dept_treasury',
          displayName: 'กรมบัญชีกลาง',
          parentId: 1,
        ),
        const Organization(
          id: 2,
          name: 'dept_revenue',
          displayName: 'กรมสรรพากร',
          parentId: 1,
        ),
        const Organization(
          id: 3,
          name: 'dept_provincial',
          displayName: 'กรมการปกครอง',
          parentId: 2,
        ),
      ];

      emit(
        AdditionalDataState.loaded(
          governmentAgencies: _governmentAgencies,
          ministries: _ministries,
          departments: _departments,
        ),
      );
    } catch (e) {
      emit(AdditionalDataState.error(message: e.toString()));
    }
  }

  /// Update first name
  void updateFirstName(String value) {
    _firstName = value;
    _clearFieldError('firstName');
  }

  /// Update last name
  void updateLastName(String value) {
    _lastName = value;
    _clearFieldError('lastName');
  }

  /// Update member type
  void updateMemberType(MemberType? value) {
    _selectedMemberType = value;
    _clearFieldError('memberType');

    // Clear government-related selections if private sector is selected (id: 2)
    if (value?.id == 2) {
      _selectedGovernmentAgency = null;
      _selectedMinistry = null;
      _selectedDepartment = null;
    }
  }

  // Backward compatibility methods
  /// @deprecated Use updateMemberType instead
  void updateSectorType(dynamic value) {
    if (value is MemberType) {
      updateMemberType(value);
    }
  }

  /// @deprecated Use selectedMemberType instead
  MemberType? get selectedSectorType => _selectedMemberType;

  /// @deprecated Use memberTypes instead
  List<MemberType> get sectorTypes => _memberTypes;

  /// Update government agency
  void updateGovernmentAgency(GovernmentSector? value) {
    _selectedGovernmentAgency = value;
    _clearFieldError('governmentAgency');
  }

  /// Update ministry
  void updateMinistry(Organization? value) {
    _selectedMinistry = value;
  }

  /// Update department
  void updateDepartment(Organization? value) {
    _selectedDepartment = value;
  }

  /// Update department name
  void updateDepartmentName(String value) {
    _departmentName = value;
    _clearFieldError('departmentName');
  }

  /// Validate form and return validation result
  bool validateForm(AppLocalizations l10n) {
    final errors = <String, String>{};

    // Validate first name
    if (_firstName.trim().isEmpty) {
      errors['firstName'] = l10n.validateFirstNameRequired;
    }

    // Validate last name
    if (_lastName.trim().isEmpty) {
      errors['lastName'] = l10n.validateLastNameRequired;
    }

    // Validate member type
    if (_selectedMemberType == null) {
      errors['memberType'] =
          l10n.validateSectorTypeRequired; // Reusing existing localization key
    }

    // Validate government agency (only for government sector - id: 1)
    if (_selectedMemberType?.id == 1 && _selectedGovernmentAgency == null) {
      errors['governmentAgency'] = l10n.validateGovernmentAgencyRequired;
    }

    // Validate department name
    if (_departmentName.trim().isEmpty) {
      errors['departmentName'] = l10n.validateDepartmentNameRequired;
    }

    if (errors.isNotEmpty) {
      emit(
        AdditionalDataState.validationError(
          firstNameError: errors['firstName'],
          lastNameError: errors['lastName'],
          sectorTypeError:
              errors['memberType'], // Using memberType error for sectorType field
          governmentAgencyError: errors['governmentAgency'],
          departmentNameError: errors['departmentName'],
        ),
      );
      return false;
    }

    emit(const AdditionalDataState.valid());
    return true;
  }

  /// Get current additional data
  AdditionalData? getAdditionalData() {
    if (_selectedMemberType == null) return null;

    return AdditionalData(
      firstName: _firstName.trim(),
      lastName: _lastName.trim(),
      memberType: _selectedMemberType!,
      governmentAgency: _selectedGovernmentAgency,
      ministry: _selectedMinistry,
      department: _selectedDepartment,
      departmentName: _departmentName.trim(),
    );
  }

  /// Clear field error when user starts typing
  void _clearFieldError(String fieldName) {
    if (state is AdditionalDataValidationError) {
      final currentState = state as AdditionalDataValidationError;

      switch (fieldName) {
        case 'firstName':
          if (currentState.firstNameError != null) {
            emit(currentState.copyWith(firstNameError: null));
          }
          break;
        case 'lastName':
          if (currentState.lastNameError != null) {
            emit(currentState.copyWith(lastNameError: null));
          }
          break;
        case 'memberType':
        case 'sectorType': // Keep backward compatibility
          if (currentState.sectorTypeError != null) {
            emit(currentState.copyWith(sectorTypeError: null));
          }
          break;
        case 'governmentAgency':
          if (currentState.governmentAgencyError != null) {
            emit(currentState.copyWith(governmentAgencyError: null));
          }
          break;
        case 'departmentName':
          if (currentState.departmentNameError != null) {
            emit(currentState.copyWith(departmentNameError: null));
          }
          break;
      }
    }
  }

  /// Get field error message
  String? getFieldError(String fieldName) {
    if (state is AdditionalDataValidationError) {
      final errorState = state as AdditionalDataValidationError;
      switch (fieldName) {
        case 'firstName':
          return errorState.firstNameError;
        case 'lastName':
          return errorState.lastNameError;
        case 'sectorType':
          return errorState.sectorTypeError;
        case 'governmentAgency':
          return errorState.governmentAgencyError;
        case 'departmentName':
          return errorState.departmentNameError;
        default:
          return null;
      }
    }
    return null;
  }

  /// Reset form data
  void reset() {
    _firstName = '';
    _lastName = '';
    _selectedMemberType = null;
    _selectedGovernmentAgency = null;
    _selectedMinistry = null;
    _selectedDepartment = null;
    _departmentName = '';
    emit(const AdditionalDataState.initial());
  }
}
