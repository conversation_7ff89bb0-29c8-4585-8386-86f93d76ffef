import 'package:equatable/equatable.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'organization.dart';

/// Additional data entity for Step 3 registration
class AdditionalData extends Equatable {
  final String firstName;
  final String lastName;
  final MemberType memberType;
  final Organization? governmentAgency;
  final Organization? ministry;
  final Organization? department;
  final String departmentName;

  const AdditionalData({
    required this.firstName,
    required this.lastName,
    required this.memberType,
    this.governmentAgency,
    this.ministry,
    this.department,
    required this.departmentName,
  });

  @override
  List<Object?> get props => [
    firstName,
    lastName,
    memberType,
    governmentAgency,
    ministry,
    department,
    departmentName,
  ];

  AdditionalData copyWith({
    String? firstName,
    String? lastName,
    MemberType? memberType,
    Organization? governmentAgency,
    Organization? ministry,
    Organization? department,
    String? departmentName,
  }) {
    return AdditionalData(
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      memberType: memberType ?? this.memberType,
      governmentAgency: governmentAgency ?? this.governmentAgency,
      ministry: ministry ?? this.ministry,
      department: department ?? this.department,
      departmentName: departmentName ?? this.departmentName,
    );
  }

  /// Check if all required fields are filled
  bool get isValid {
    return firstName.isNotEmpty &&
        lastName.isNotEmpty &&
        departmentName.isNotEmpty &&
        (memberType.id == 2 ||
            governmentAgency != null); // id: 2 is private sector
  }
}
