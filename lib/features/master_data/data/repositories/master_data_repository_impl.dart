import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/api/network_info.dart';
import '../../domain/entities/member_type.dart';
import '../../domain/repositories/master_data_repository.dart';
import '../datasources/master_data_api_data_source.dart';
import '../models/member_type_model.dart';

class MasterDataRepositoryImpl implements MasterDataRepository {
  final MasterDataApiDataSource apiDataSource;
  final NetworkInfo networkInfo;

  const MasterDataRepositoryImpl({
    required this.apiDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<MemberType>>> getMemberTypes() async {
    if (await networkInfo.isConnected) {
      try {
        final result = await apiDataSource.getMemberTypes();

        if (result.isSuccess) {
          final memberTypes =
              result.data!.map((model) => model.toEntity()).toList();
          return Right(memberTypes);
        } else {
          return Left(
            ServerFailure(
              message: result.errorMessage ?? 'Failed to get member types',
            ),
          );
        }
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
