import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_endpoints.dart';
import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/core/api/model/api_response_parser.dart';
import 'package:mcdc/core/error/exceptions.dart';
import '../models/member_type_model.dart';
import '../models/government_sector_model.dart';

abstract class MasterDataApiDataSource {
  Future<Result<List<MemberTypeModel>>> getMemberTypes();
  Future<Result<List<GovernmentSectorModel>>> getGovernmentSectors();
}

class MasterDataApiDataSourceImpl implements MasterDataApiDataSource {
  final ApiClient _apiClient;

  const MasterDataApiDataSourceImpl(this._apiClient);

  @override
  Future<Result<List<MemberTypeModel>>> getMemberTypes() async {
    final result = await _apiClient.get<dynamic>(
      ApiEndpoints.memberTypes,
      queryParameters: {'page_size': 999},
    );

    // Use the new Result-based approach with wrapped list parsing
    return result.map<List<MemberTypeModel>>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Parse as wrapped list response (data is inside a "data" key)
        final parseResult = ApiResponseParser.parseWrappedListResponse(
          responseData,
          MemberTypeModel.fromApiJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<List<GovernmentSectorModel>>> getGovernmentSectors() async {
    final result = await _apiClient.get<dynamic>(
      ApiEndpoints.governmentSectors,
      queryParameters: {'page_size': 999},
    );

    // Use the new Result-based approach with wrapped list parsing
    return result.map<List<GovernmentSectorModel>>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Parse as wrapped list response (data is inside a "data" key)
        final parseResult = ApiResponseParser.parseWrappedListResponse(
          responseData,
          GovernmentSectorModel.fromApiJson,
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }
}
