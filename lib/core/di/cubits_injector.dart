import 'package:get_it/get_it.dart';
import 'package:mcdc/shared/presentation/blocs/language/language_bloc.dart';
import 'package:mcdc/features/poll/presentation/bloc/feedback_bloc.dart';
import 'package:mcdc/features/user/presentation/bloc/delete_account_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/edit_password_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/edit_profile_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/auth_status_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/check_id_card_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/user_login_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/user_logout_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_cubit.dart';

/// Initialize all cubits used in the application
void initCubits(GetIt sl) {
  // Language BLoC
  sl.registerLazySingleton<LanguageBloc>(() => LanguageBloc());

  // Feedback BLoC
  sl.registerFactory<FeedbackBloc>(
    () => FeedbackBloc(getFeedbackQuestions: sl(), submitFeedback: sl()),
  );

  // Edit Profile Cubit
  sl.registerFactory<EditProfileCubit>(
    () => EditProfileCubit(getUserProfile: sl(), updateUserProfile: sl()),
  );

  // Edit Password Cubit
  sl.registerFactory<EditPasswordCubit>(
    () => EditPasswordCubit(changePassword: sl()),
  );

  // Delete Account Cubit
  sl.registerFactory<DeleteAccountCubit>(
    () => DeleteAccountCubit(deleteAccount: sl()),
  );

  // Auth Status Cubit
  sl.registerFactory<AuthStatusCubit>(
    () => AuthStatusCubit(checkLoginStatus: sl()),
  );

  // Check ID Card Cubit
  sl.registerFactory<CheckIdCardCubit>(
    () => CheckIdCardCubit(checkDuplicateIdCard: sl()),
  );

  // Validate Register Cubit
  sl.registerFactory<ValidateRegisterCubit>(
    () => ValidateRegisterCubit(validateMemberRegister: sl()),
  );

  // User Login Cubit
  sl.registerFactory<UserLoginCubit>(() => UserLoginCubit(memberLogin: sl()));

  // User Logout Cubit
  sl.registerFactory<UserLogoutCubit>(
    () => UserLogoutCubit(memberLogout: sl()),
  );

  // Additional Data Cubit
  sl.registerFactory<AdditionalDataCubit>(
    () => AdditionalDataCubit(sl(), sl()),
  );
}
