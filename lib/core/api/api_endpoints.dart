import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiEndpoints {
  ApiEndpoints._();

  static String baseUrl = dotenv.get('API_BASE_URL', fallback: '');
  static const String memberLogin = '/member/login/';
  static const String memberCheckIdCard = '/member/check-pid/';
  static const String validateMemberRegister = '/member/register/';

  // OTP endpoints
  static const String otpGenerate = '/otp/generate/';
  static const String otpVerify = '/otp/verify/';

  // Master data endpoints
  static const String memberTypes = '/mas/member-type';
  static const String governmentSectors = '/mas/government-sector';
}
