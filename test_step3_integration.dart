import 'package:flutter_test/flutter_test.dart';
import 'package:dartz/dartz.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/usecases/get_member_types.dart';
import 'package:mcdc/features/master_data/domain/repositories/master_data_repository.dart';
import 'package:mcdc/features/user/domain/entities/organization.dart';
import 'package:mcdc/features/user/domain/entities/additional_data.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_cubit.dart';

// Simple mock implementation
class MockGetMemberTypes implements GetMemberTypes {
  final List<MemberType> _memberTypes;

  MockGetMemberTypes(this._memberTypes);

  @override
  MasterDataRepository get repository => throw UnimplementedError(); // Not used in tests

  @override
  Future<Either<Failure, List<MemberType>>> call(NoParams params) async {
    return Right(_memberTypes);
  }
}

void main() {
  group('Step 3 Additional Data Tests', () {
    late AdditionalDataCubit cubit;
    late MockGetMemberTypes mockGetMemberTypes;

    // Mock member types
    final mockMemberTypes = [
      const MemberType(id: 1, nameTh: 'ภาครัฐ', nameEn: 'Government sector'),
      const MemberType(id: 2, nameTh: 'ภาคเอกชน', nameEn: 'Private sector'),
    ];

    setUp(() {
      mockGetMemberTypes = MockGetMemberTypes(mockMemberTypes);
      cubit = AdditionalDataCubit(mockGetMemberTypes);
    });

    tearDown(() {
      cubit.close();
    });

    test('should initialize with empty data', () {
      expect(cubit.firstName, isEmpty);
      expect(cubit.lastName, isEmpty);
      expect(cubit.selectedMemberType, isNull);
      expect(cubit.departmentName, isEmpty);
    });

    test('should update first name', () {
      const testName = 'John';
      cubit.updateFirstName(testName);
      expect(cubit.firstName, equals(testName));
    });

    test('should update last name', () {
      const testName = 'Doe';
      cubit.updateLastName(testName);
      expect(cubit.lastName, equals(testName));
    });

    test('should update member type', () {
      final governmentType = mockMemberTypes.first; // Government sector
      cubit.updateMemberType(governmentType);
      expect(cubit.selectedMemberType, equals(governmentType));
    });

    test('should update department name', () {
      const testDepartment = 'IT Department';
      cubit.updateDepartmentName(testDepartment);
      expect(cubit.departmentName, equals(testDepartment));
    });

    test('should provide member types after initialization', () async {
      cubit.initialize();
      // Wait a bit for the async operation to complete
      await Future.delayed(const Duration(milliseconds: 100));
      final memberTypes = cubit.memberTypes;
      expect(memberTypes, isNotEmpty);
      expect(memberTypes.length, equals(2));
      expect(memberTypes.any((type) => type.id == 1), isTrue); // Government
      expect(memberTypes.any((type) => type.id == 2), isTrue); // Private
    });

    test(
      'should create additional data when all required fields are filled',
      () {
        final privateType = mockMemberTypes.last; // Private sector
        cubit.updateFirstName('John');
        cubit.updateLastName('Doe');
        cubit.updateMemberType(privateType);
        cubit.updateDepartmentName('IT Department');

        final additionalData = cubit.getAdditionalData();
        expect(additionalData, isNotNull);
        expect(additionalData!.firstName, equals('John'));
        expect(additionalData.lastName, equals('Doe'));
        expect(additionalData.memberType, equals(privateType));
        expect(additionalData.departmentName, equals('IT Department'));
      },
    );

    test('should return null when required fields are missing', () {
      cubit.updateFirstName('John');
      // Missing last name, sector type, and department name

      final additionalData = cubit.getAdditionalData();
      expect(additionalData, isNull);
    });
  });

  group('MemberType Tests', () {
    test('should have correct government member type', () {
      const governmentType = MemberType(
        id: 1,
        nameTh: 'ภาครัฐ',
        nameEn: 'Government sector',
      );
      expect(governmentType.id, equals(1));
      expect(governmentType.nameTh, equals('ภาครัฐ'));
      expect(governmentType.nameEn, equals('Government sector'));
      expect(governmentType.displayName, equals('ภาครัฐ'));
    });

    test('should have correct private member type', () {
      const privateType = MemberType(
        id: 2,
        nameTh: 'ภาคเอกชน',
        nameEn: 'Private sector',
      );
      expect(privateType.id, equals(2));
      expect(privateType.nameTh, equals('ภาคเอกชน'));
      expect(privateType.nameEn, equals('Private sector'));
      expect(privateType.displayName, equals('ภาคเอกชน'));
    });
  });

  group('Organization Tests', () {
    test('should create organization with required fields', () {
      const org = Organization(
        id: 1,
        name: 'test_org',
        displayName: 'Test Organization',
      );

      expect(org.id, equals(1));
      expect(org.name, equals('test_org'));
      expect(org.displayName, equals('Test Organization'));
      expect(org.parentId, isNull);
    });

    test('should create organization with parent', () {
      const org = Organization(
        id: 2,
        name: 'child_org',
        displayName: 'Child Organization',
        parentId: 1,
      );

      expect(org.parentId, equals(1));
    });
  });

  group('AdditionalData Tests', () {
    const privateType = MemberType(
      id: 2,
      nameTh: 'ภาคเอกชน',
      nameEn: 'Private sector',
    );
    const governmentType = MemberType(
      id: 1,
      nameTh: 'ภาครัฐ',
      nameEn: 'Government sector',
    );

    test(
      'should be valid when all required fields are provided for private sector',
      () {
        const data = AdditionalData(
          firstName: 'John',
          lastName: 'Doe',
          memberType: privateType,
          departmentName: 'IT Department',
        );

        expect(data.isValid, isTrue);
      },
    );

    test(
      'should be valid when all required fields are provided for government sector',
      () {
        const governmentAgency = Organization(
          id: 1,
          name: 'central_gov',
          displayName: 'Central Government',
        );

        const data = AdditionalData(
          firstName: 'Jane',
          lastName: 'Smith',
          memberType: governmentType,
          governmentAgency: governmentAgency,
          departmentName: 'HR Department',
        );

        expect(data.isValid, isTrue);
      },
    );

    test(
      'should be invalid when government sector is selected but no agency is provided',
      () {
        const data = AdditionalData(
          firstName: 'Jane',
          lastName: 'Smith',
          memberType: governmentType,
          departmentName: 'HR Department',
        );

        expect(data.isValid, isFalse);
      },
    );

    test('should be invalid when required fields are empty', () {
      const data = AdditionalData(
        firstName: '',
        lastName: 'Smith',
        memberType: privateType,
        departmentName: 'HR Department',
      );

      expect(data.isValid, isFalse);
    });
  });
}
